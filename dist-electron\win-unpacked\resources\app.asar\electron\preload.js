const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Add any APIs you want to expose to the renderer process here
  platform: process.platform,
  versions: process.versions,

  // Example: Send message to main process
  sendMessage: (message) => ipcRenderer.invoke('send-message', message),

  // Example: Listen for messages from main process
  onMessage: (callback) => ipcRenderer.on('message', callback),

  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;
