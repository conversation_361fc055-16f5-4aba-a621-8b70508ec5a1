
import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface TranscriptionDisplayProps {
  transcription: string;
  interimText: string;
  isRecording: boolean;
}

const TranscriptionDisplay = ({ transcription, interimText, isRecording }: TranscriptionDisplayProps) => {
  const hasContent = transcription.length > 0 || interimText.length > 0;

  return (
    <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
      <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-4">
        <h2 className="text-white font-semibold text-lg">Live Transcription</h2>
      </div>
      
      <div className="p-6">
        <ScrollArea className="h-96 w-full">
          {!hasContent && !isRecording ? (
            <div className="flex items-center justify-center h-full text-gray-400">
              <div className="text-center">
                <div className="text-6xl mb-4">🎤</div>
                <p className="text-lg">Click "Start Recording" to begin transcription</p>
                <p className="text-sm mt-2">Your speech will appear here in real-time</p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {/* Final transcription text */}
              {transcription && (
                <div className="text-gray-800 leading-relaxed text-lg">
                  {transcription}
                </div>
              )}
              
              {/* Interim (live) text */}
              {interimText && (
                <div className="text-blue-600 leading-relaxed text-lg opacity-75 italic">
                  {interimText}
                  <span className="inline-block w-1 h-6 bg-blue-500 ml-1 animate-pulse"></span>
                </div>
              )}
              
              {/* Waiting for speech indicator */}
              {isRecording && !interimText && !transcription && (
                <div className="text-gray-500 italic flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-75"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                  </div>
                  Listening for speech...
                </div>
              )}
            </div>
          )}
        </ScrollArea>
      </div>
      
      {/* Footer with word count */}
      {hasContent && (
        <div className="border-t bg-gray-50 px-6 py-3">
          <div className="text-sm text-gray-500">
            Words: {transcription.trim().split(/\s+/).filter(word => word.length > 0).length}
            {interimText && ` (+${interimText.trim().split(/\s+/).filter(word => word.length > 0).length} pending)`}
          </div>
        </div>
      )}
    </div>
  );
};

export default TranscriptionDisplay;
