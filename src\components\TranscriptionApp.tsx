import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Mic, MicOff, Square, Trash2 } from 'lucide-react';
import ControlButton from './ControlButton';
import TranscriptionDisplay from './TranscriptionDisplay';
import { useToast } from '@/hooks/use-toast';

const TranscriptionApp = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcription, setTranscription] = useState('');
  const [interimText, setInterimText] = useState('');
  const [isSupported, setIsSupported] = useState(true);
  const [shouldRestart, setShouldRestart] = useState(false);
  const [consecutiveErrors, setConsecutiveErrors] = useState(0);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManualStopRef = useRef(false);
  const lastActivityTimeRef = useRef<number>(Date.now());
  const { toast } = useToast();

  useEffect(() => {
    // Check if Speech Recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      setIsSupported(false);
      toast({
        title: "Speech Recognition Not Supported",
        description: "Your browser doesn't support speech recognition. Please use Chrome or Edge.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const createRecognition = useCallback(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
      console.log('Speech recognition started');
      setIsRecording(true);
      setConsecutiveErrors(0);
      lastActivityTimeRef.current = Date.now();
      if (!shouldRestart) {
        toast({
          title: "Recording Started",
          description: "Listening for speech...",
        });
      }
    };

    recognition.onresult = (event) => {
      lastActivityTimeRef.current = Date.now();
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript + ' ';
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        setTranscription(prev => prev + finalTranscript);
      }
      setInterimText(interimTranscript);
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      
      if (event.error === 'not-allowed') {
        setIsRecording(false);
        setInterimText('');
        isManualStopRef.current = true;
        toast({
          title: "Recording Error",
          description: 'Microphone access denied. Please allow microphone permissions.',
          variant: "destructive",
        });
        return;
      }
      
      // Increment error counter
      setConsecutiveErrors(prev => prev + 1);
      
      // For network errors and other recoverable errors, restart more aggressively
      if ((event.error === 'network' || event.error === 'audio-capture' || event.error === 'no-speech') 
          && shouldRestart && !isManualStopRef.current) {
        console.log(`Attempting to restart recognition due to ${event.error} error (attempt ${consecutiveErrors + 1})`);
        
        // Use shorter delays for network errors to maintain continuity
        const restartDelay = event.error === 'network' ? 50 : 200;
        restartRecognition(restartDelay);
      }
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
      setInterimText('');
      
      // Auto-restart if we should be recording and it wasn't a manual stop
      if (shouldRestart && !isManualStopRef.current) {
        console.log('Auto-restarting recognition...');
        
        // Use different restart delays based on consecutive errors
        let restartDelay = 100;
        if (consecutiveErrors > 3) {
          restartDelay = 500;
        } else if (consecutiveErrors > 1) {
          restartDelay = 250;
        }
        
        restartRecognition(restartDelay);
      } else {
        setIsRecording(false);
      }
    };

    return recognition;
  }, [shouldRestart, toast, consecutiveErrors]);

  const restartRecognition = useCallback((delay: number = 100) => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.log('Error stopping recognition:', error);
      }
    }
    
    // Clear any existing timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
    }
    
    restartTimeoutRef.current = setTimeout(() => {
      if (shouldRestart && !isManualStopRef.current) {
        try {
          const newRecognition = createRecognition();
          recognitionRef.current = newRecognition;
          newRecognition.start();
        } catch (error) {
          console.error('Error restarting recognition:', error);
          // If restart fails, try again with longer delay
          if (shouldRestart && !isManualStopRef.current && consecutiveErrors < 10) {
            restartRecognition(Math.min(delay * 2, 2000));
          }
        }
      }
    }, delay);
  }, [createRecognition, shouldRestart, consecutiveErrors]);

  // Monitor for extended periods without activity and restart if needed
  useEffect(() => {
    if (!shouldRestart || isManualStopRef.current) return;

    const activityCheckInterval = setInterval(() => {
      const timeSinceLastActivity = Date.now() - lastActivityTimeRef.current;
      
      // If no activity for 5 seconds and we should be recording, restart
      if (timeSinceLastActivity > 5000 && isRecording) {
        console.log('No activity detected, restarting recognition...');
        restartRecognition(100);
      }
    }, 2000);

    return () => clearInterval(activityCheckInterval);
  }, [shouldRestart, isRecording, restartRecognition]);

  const startRecording = useCallback(() => {
    if (!isSupported) return;

    isManualStopRef.current = false;
    setShouldRestart(true);
    setConsecutiveErrors(0);
    lastActivityTimeRef.current = Date.now();
    
    // Clear any existing timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    try {
      const recognition = createRecognition();
      recognitionRef.current = recognition;
      recognition.start();
    } catch (error) {
      console.error('Error starting recognition:', error);
      toast({
        title: "Recording Error",
        description: "Failed to start speech recognition. Please try again.",
        variant: "destructive",
      });
    }
  }, [isSupported, createRecognition, toast]);

  const stopRecording = useCallback(() => {
    isManualStopRef.current = true;
    setShouldRestart(false);
    setConsecutiveErrors(0);
    
    // Clear any restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
      restartTimeoutRef.current = null;
    }

    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.log('Error stopping recognition:', error);
      }
      setIsRecording(false);
      setInterimText('');
      toast({
        title: "Recording Stopped",
        description: "Transcription saved.",
      });
    }
  }, [toast]);

  const clearTranscription = useCallback(() => {
    setTranscription('');
    setInterimText('');
    toast({
      title: "Transcription Cleared",
      description: "Text has been reset.",
    });
  }, [toast]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.log('Cleanup error:', error);
        }
      }
    };
  }, []);

  if (!isSupported) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md">
          <div className="text-red-500 mb-4">
            <MicOff size={48} className="mx-auto" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Not Supported</h2>
          <p className="text-gray-600">
            Speech recognition is not supported in your current browser. Please use Chrome or Edge for the best experience.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Live Transcription
          </h1>
          <p className="text-gray-600 text-lg">
            Real-time voice-to-text transcription powered by Web Speech API
          </p>
        </div>

        {/* Control Panel */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
          <div className="flex items-center justify-center gap-4">
            <ControlButton
              onClick={startRecording}
              disabled={isRecording}
              variant="primary"
              icon={<Mic size={20} />}
            >
              Start Recording
            </ControlButton>
            
            <ControlButton
              onClick={stopRecording}
              disabled={!isRecording}
              variant="secondary"
              icon={<Square size={20} />}
            >
              Stop Recording
            </ControlButton>
            
            <ControlButton
              onClick={clearTranscription}
              disabled={isRecording}
              variant="danger"
              icon={<Trash2 size={20} />}
            >
              Clear
            </ControlButton>
          </div>

          {/* Recording Status */}
          {isRecording && (
            <div className="mt-4 flex items-center justify-center gap-2 text-green-600">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Recording in progress...</span>
              {consecutiveErrors > 0 && (
                <span className="text-xs text-orange-500 ml-2">
                  (Auto-recovering from connection issues)
                </span>
              )}
            </div>
          )}
        </div>

        {/* Transcription Display */}
        <TranscriptionDisplay
          transcription={transcription}
          interimText={interimText}
          isRecording={isRecording}
        />
      </div>
    </div>
  );
};

export default TranscriptionApp;
