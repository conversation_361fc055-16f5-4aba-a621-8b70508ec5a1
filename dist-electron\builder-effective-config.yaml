directories:
  output: dist-electron
  buildResources: build
appId: com.echoscribe.desktop
productName: Echo Scribe Desktop
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - node_modules/**/*
extraResources:
  - from: public
    to: public
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
linux:
  target:
    - target: AppImage
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 36.3.1
